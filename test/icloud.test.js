const assert = require('assert');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('Starting iCloud sync tests...');

// Mock iCloud paths for testing
const testDir = path.join(os.tmpdir(), 'red-tasks-test');
const testDataFile = path.join(testDir, 'tasks.json');
const testMetaFile = path.join(testDir, 'sync-meta.json');

// Clean up test directory
function cleanupTestDir() {
  if (fs.existsSync(testDir)) {
    fs.rmSync(testDir, { recursive: true, force: true });
  }
}

// Setup test directory
function setupTestDir() {
  cleanupTestDir();
  fs.mkdirSync(testDir, { recursive: true });
}

// Test data
const testTasks = [
  {
    id: 1,
    description: 'Test task 1',
    tags: ['work', 'urgent'],
    deadline: '2024-12-31',
    priority: 'ASAP'
  },
  {
    id: 2,
    description: 'Test task 2',
    tags: ['Home/Chores'],
    deadline: null,
    priority: 'Anytime'
  }
];

// Test saving tasks to iCloud format
console.log('Testing save to iCloud format...');
setupTestDir();

// Save test data
fs.writeFileSync(testDataFile, JSON.stringify(testTasks, null, 2));

// Save metadata
const syncMeta = {
  lastSync: new Date().toISOString(),
  taskCount: testTasks.length,
  version: '1.0'
};
fs.writeFileSync(testMetaFile, JSON.stringify(syncMeta, null, 2));

// Verify files were created
assert(fs.existsSync(testDataFile), 'Data file should exist');
assert(fs.existsSync(testMetaFile), 'Meta file should exist');

// Test loading tasks from iCloud format
console.log('Testing load from iCloud format...');
const loadedData = JSON.parse(fs.readFileSync(testDataFile, 'utf8'));
const loadedMeta = JSON.parse(fs.readFileSync(testMetaFile, 'utf8'));

assert.strictEqual(loadedData.length, 2, 'Should load 2 tasks');
assert.strictEqual(loadedData[0].description, 'Test task 1', 'First task description should match');
assert.strictEqual(loadedData[1].tags[0], 'Home/Chores', 'Second task should have folder tag');
assert.strictEqual(loadedMeta.taskCount, 2, 'Meta should show correct task count');
assert(loadedMeta.lastSync, 'Meta should have lastSync timestamp');

// Test data format conversion
console.log('Testing data format conversion...');

// SQLite format (what the database uses)
const sqliteTask = {
  id: 1,
  name: 'Test task',
  date: '2024-12-31',
  type: 'ASAP',
  tags: ['work', 'urgent']
};

// JSON format (what iCloud uses)
const jsonTask = {
  id: 1,
  description: 'Test task',
  deadline: '2024-12-31',
  priority: 'ASAP',
  tags: ['work', 'urgent']
};

// Test conversion functions (simulated)
function convertSQLiteTaskToJSON(sqliteTask) {
  return {
    id: sqliteTask.id,
    description: sqliteTask.name,
    tags: sqliteTask.tags || [],
    deadline: sqliteTask.date,
    priority: sqliteTask.type
  };
}

function convertJSONTaskToSQLite(jsonTask) {
  return {
    id: jsonTask.id,
    name: jsonTask.description,
    date: jsonTask.deadline,
    type: jsonTask.priority,
    tags: jsonTask.tags || []
  };
}

const convertedToJSON = convertSQLiteTaskToJSON(sqliteTask);
assert.deepStrictEqual(convertedToJSON, jsonTask, 'SQLite to JSON conversion should work');

const convertedToSQLite = convertJSONTaskToSQLite(jsonTask);
assert.deepStrictEqual(convertedToSQLite, sqliteTask, 'JSON to SQLite conversion should work');

// Test folder tag validation in iCloud context
console.log('Testing folder tag validation for iCloud sync...');

const tasksWithFolderTags = [
  {
    id: 1,
    description: 'Task with folder tag',
    tags: ['Work/Projects', 'urgent'],
    deadline: null,
    priority: null
  },
  {
    id: 2,
    description: 'Task with standalone tag',
    tags: ['personal'],
    deadline: null,
    priority: null
  }
];

// Save and reload to test folder tag persistence
fs.writeFileSync(testDataFile, JSON.stringify(tasksWithFolderTags, null, 2));
const reloadedTasks = JSON.parse(fs.readFileSync(testDataFile, 'utf8'));

assert.strictEqual(reloadedTasks[0].tags[0], 'Work/Projects', 'Folder tag should persist');
assert.strictEqual(reloadedTasks[1].tags[0], 'personal', 'Standalone tag should persist');

// Test sync metadata validation
console.log('Testing sync metadata validation...');

const validMeta = {
  lastSync: '2024-01-01T12:00:00.000Z',
  taskCount: 5,
  version: '1.0'
};

const invalidMeta = {
  lastSync: 'invalid-date',
  taskCount: 'not-a-number',
  version: null
};

// Valid metadata should parse correctly
assert(new Date(validMeta.lastSync).getTime() > 0, 'Valid lastSync should parse as date');
assert.strictEqual(typeof validMeta.taskCount, 'number', 'taskCount should be number');
assert.strictEqual(typeof validMeta.version, 'string', 'version should be string');

// Test error handling for corrupted data
console.log('Testing error handling for corrupted data...');

// Write invalid JSON
fs.writeFileSync(testDataFile, 'invalid json content');

try {
  JSON.parse(fs.readFileSync(testDataFile, 'utf8'));
  assert.fail('Should throw error for invalid JSON');
} catch (error) {
  assert(error instanceof SyntaxError, 'Should throw SyntaxError for invalid JSON');
}

// Cleanup
cleanupTestDir();

console.log('iCloud sync tests passed!');
