const {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Container,
  <PERSON>,
  CssB<PERSON><PERSON>,
  <PERSON>er,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Button,
  TextField,
  Card,
  CardContent,
  Chip,
  Stack,
  Paper,
  ThemeProvider,
  createTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  FormControl,
  FormLabel,
  ToggleButtonGroup,
  ToggleButton,
  Menu,
  MenuItem
} = MaterialUI;

// Tema Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#d32f2f', // Rosso per "Red Tasks"
    },
    secondary: {
      main: '#f44336',
    },
  },
});

// Icone Material-UI
const MenuIcon = () => <span className="material-icons">menu</span>;
const AddIcon = () => <span className="material-icons">add</span>;
const InboxIcon = () => <span className="material-icons">inbox</span>;
const LabelIcon = () => <span className="material-icons">label</span>;
const EditIcon = () => <span className="material-icons">edit</span>;
const DeleteIcon = () => <span className="material-icons">delete</span>;
const FolderIcon = () => <span className="material-icons">folder</span>;
const FolderOpenIcon = () => <span className="material-icons">folder_open</span>;
const ExpandMoreIcon = () => <span className="material-icons">expand_more</span>;
const ChevronRightIcon = () => <span className="material-icons">chevron_right</span>;
const ViewListIcon = () => <span className="material-icons">view_list</span>;
const SearchIcon = () => <span className="material-icons">search</span>;
const CloudIcon = () => <span className="material-icons">cloud</span>;
const CloudOffIcon = () => <span className="material-icons">cloud_off</span>;
const KeyboardArrowUpIcon = () => <span className="material-icons">keyboard_arrow_up</span>;
const KeyboardArrowDownIcon = () => <span className="material-icons">keyboard_arrow_down</span>;
const CloseIcon = () => <span className="material-icons">close</span>;
const CheckCircleIcon = () => <span className="material-icons">check_circle</span>;
const CalendarTodayIcon = () => <span className="material-icons">calendar_today</span>;

// Import Tasks module
const Tasks = window.Tasks;

function App() {
  const [tasks, setTasks] = React.useState([]);
  const [syncStatus, setSyncStatus] = React.useState(null);
  const [description, setDescription] = React.useState('');
  const [selectedTags, setSelectedTags] = React.useState([]);
  const [deadline, setDeadline] = React.useState('');
  const [notes, setNotes] = React.useState('');
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [priorityType, setPriorityType] = React.useState('');
  const [editingTask, setEditingTask] = React.useState(null);
  const [contextMenu, setContextMenu] = React.useState(null);
  const [scrollProgress, setScrollProgress] = React.useState(0);
  const [currentPage, setCurrentPage] = React.useState(0);
  const [tasksPerPage, setTasksPerPage] = React.useState(3); // Numero di task per pagina (dinamico)
  const [windowHeight, setWindowHeight] = React.useState(window.innerHeight);
  const [tagError, setTagError] = React.useState('');
  const [expandedFolders, setExpandedFolders] = React.useState(new Set());
  const [tagInputValue, setTagInputValue] = React.useState('');
  const [dateError, setDateError] = React.useState('');
  const [viewMode, setViewMode] = React.useState('inbox'); // 'inbox', 'all', or tag name
  const [searchQuery, setSearchQuery] = React.useState('');
  const [taskDetailsOpen, setTaskDetailsOpen] = React.useState(false);
  const [selectedTaskForDetails, setSelectedTaskForDetails] = React.useState(null);

  const tags = Tasks.uniqueTags(tasks);
  const folders = Tasks.extractFolders(tags);
  const standaloneTags = Tasks.getStandaloneTags(tags);

  // Logica di visualizzazione task basata sulla modalità
  let allDisplayed = [];
  let calendarData = null;

  if (viewMode === 'inbox') {
    allDisplayed = Tasks.getInboxTasks(tasks);
  } else if (viewMode === 'all') {
    allDisplayed = [...tasks]
      .filter(task => !task.completed) // Escludi task completati
      .sort((a, b) => {
        const descA = typeof a.description === 'string' ? a.description : '';
        const descB = typeof b.description === 'string' ? b.description : '';
        return descA.localeCompare(descB);
      });
    // Applica ricerca fuzzy se presente
    if (searchQuery.trim()) {
      allDisplayed = Tasks.fuzzySearchTasks(allDisplayed, searchQuery);
    }
  } else if (viewMode === 'calendario') {
    calendarData = getCalendarData();
    allDisplayed = []; // Non mostriamo task nella view calendario
  } else {
    // Modalità tag specifico
    allDisplayed = Tasks.filterTasksByTag(tasks, viewMode);
  }

  // Calcola paginazione
  const totalPages = Math.ceil(allDisplayed.length / tasksPerPage);
  const startIndex = currentPage * tasksPerPage;
  const endIndex = startIndex + tasksPerPage;
  const displayed = allDisplayed.slice(startIndex, endIndex);

  const priorityTypes = ['Evento', 'Deadline', 'ASAP', 'Anytime'];
  const needsDate = priorityType === 'Evento' || priorityType === 'Deadline';
  const isEditing = editingTask !== null;

  // Funzione per calcolare il numero di task per pagina in base alla grandezza della finestra
  function calculateTasksPerPage(height) {
    // Altezza approssimativa di ogni task card (inclusi spacing e padding)
    const taskCardHeight = 140; // Base height per una card con contenuto standard

    // Altezza riservata per header, toolbar, paginazione, etc.
    const reservedHeight = 300; // AppBar + padding + controlli + margini

    // Altezza disponibile per le task cards
    const availableHeight = height - reservedHeight;

    // Calcola quante task possono fittare
    const calculatedTasks = Math.floor(availableHeight / taskCardHeight);

    // Minimo 2 task, massimo 10 task per pagina
    const result = Math.max(2, Math.min(10, calculatedTasks));

    console.log(`Window height: ${height}px, Available: ${availableHeight}px, Tasks per page: ${result}`);
    return result;
  }

  // Load tasks and sync status on app start
  React.useEffect(() => {
    async function loadData() {
      try {
        // Load tasks from database (which now syncs with iCloud)
        const loadedTasks = await window.api.getTasks();
        setTasks(loadedTasks);

        // Get sync status
        const status = await window.api.getSyncStatus();
        setSyncStatus(status);

        console.log('Loaded tasks:', loadedTasks.length);
        console.log('iCloud status:', status);
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    }

    loadData();
  }, []);

  // Reset current page when view mode or search query changes
  React.useEffect(() => {
    setCurrentPage(0);
  }, [viewMode, searchQuery]);

  // Handle window resize to adjust tasks per page
  React.useEffect(() => {
    function handleResize() {
      const newHeight = window.innerHeight;
      setWindowHeight(newHeight);

      const newTasksPerPage = calculateTasksPerPage(newHeight);
      if (newTasksPerPage !== tasksPerPage) {
        setTasksPerPage(newTasksPerPage);
        // Reset to first page when tasks per page changes to avoid empty pages
        setCurrentPage(0);
      }
    }

    // Set initial tasks per page
    const initialTasksPerPage = calculateTasksPerPage(window.innerHeight);
    setTasksPerPage(initialTasksPerPage);

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [tasksPerPage]); // Include tasksPerPage in dependencies to avoid stale closure

  // Keyboard navigation
  React.useEffect(() => {
    function handleKeyDown(event) {
      // Only handle keyboard navigation when no dialog is open
      if (!dialogOpen && totalPages > 1) {
        if (event.key === 'ArrowLeft' || event.key === 'ArrowUp') {
          event.preventDefault();
          goToPreviousPage();
        } else if (event.key === 'ArrowRight' || event.key === 'ArrowDown') {
          event.preventDefault();
          goToNextPage();
        } else if (event.key >= '1' && event.key <= '9') {
          const pageNumber = parseInt(event.key) - 1;
          if (pageNumber < totalPages) {
            event.preventDefault();
            goToPage(pageNumber);
          }
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [dialogOpen, totalPages, currentPage]);

  function openEditDialog(task) {
    setEditingTask(task);
    setDescription(task.description);
    setSelectedTags(task.tags);
    setDeadline(task.deadline || '');
    setPriorityType(task.priority || '');
    setNotes(task.notes || '');
    setTagError('');
    setTagInputValue('');
    setDateError('');
    setDialogOpen(true);
    setContextMenu(null);
  }

  function openCreateDialog() {
    setEditingTask(null);
    setDescription('');
    setSelectedTags([]);
    setDeadline('');
    setPriorityType('');
    setNotes('');
    setTagError('');
    setTagInputValue('');
    setDateError('');
    setDialogOpen(true);
  }

  function handleSearchChange(event) {
    setSearchQuery(event.target.value);
  }

  function handleContextMenu(event, task) {
    event.preventDefault();
    console.log('Context menu opened for task:', task.description, 'completed:', task.completed);
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      task: task
    });
  }

  function handleTaskClick(task) {
    setSelectedTaskForDetails(task);
    setTaskDetailsOpen(true);
  }

  function closeTaskDetails() {
    setTaskDetailsOpen(false);
    setSelectedTaskForDetails(null);
  }

  function closeContextMenu() {
    setContextMenu(null);
  }

  // Funzioni per il calendario
  function getCalendarData() {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    // Primo giorno del mese
    const firstDay = new Date(currentYear, currentMonth, 1);
    // Ultimo giorno del mese
    const lastDay = new Date(currentYear, currentMonth + 1, 0);

    // Primo giorno della settimana da mostrare (lunedì della settimana del primo giorno)
    const startDate = new Date(firstDay);
    const dayOfWeek = firstDay.getDay();
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Lunedì = 0
    startDate.setDate(firstDay.getDate() - daysToSubtract);

    // Ultimo giorno della settimana da mostrare
    const endDate = new Date(lastDay);
    const lastDayOfWeek = lastDay.getDay();
    const daysToAdd = lastDayOfWeek === 0 ? 0 : 7 - lastDayOfWeek;
    endDate.setDate(lastDay.getDate() + daysToAdd);

    const days = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dateStr = Tasks.dateToLocalString(currentDate);

      // Task completati in questo giorno
      const completedTasks = tasks.filter(task => {
        if (!task.completedAt) return false;
        const completedDate = Tasks.dateToLocalString(new Date(task.completedAt));
        return completedDate === dateStr;
      });

      // Task che erano nell'inbox per questo giorno (simuliamo la logica inbox per quella data)
      const dayInboxTasks = tasks.filter(task => {
        // Task non completati che sarebbero stati nell'inbox quel giorno
        if (task.completed) {
          // Se il task è completato, controlla se è stato completato dopo questa data
          const completedDate = Tasks.dateToLocalString(new Date(task.completedAt));
          if (completedDate <= dateStr) {
            // Task completato prima o in questo giorno - era nell'inbox
            return true;
          }
        }

        // Task non completati che sarebbero nell'inbox
        if (!task.completed) {
          // ASAP tasks sempre in inbox
          if (task.priority === 'ASAP') return true;

          // Anytime tasks mai in inbox
          if (task.priority === 'Anytime') return false;

          // Task senza priorità in inbox
          if (!task.priority) return true;

          // Evento: solo se la data è quel giorno
          if (task.priority === 'Evento' && task.deadline) {
            return task.deadline === dateStr;
          }

          // Deadline: se la data è quel giorno o nel futuro
          if (task.priority === 'Deadline' && task.deadline) {
            return task.deadline >= dateStr;
          }
        }

        return false;
      });

      const totalInboxTasks = dayInboxTasks.length;
      const completedCount = completedTasks.length;
      const completionPercentage = totalInboxTasks > 0 ? (completedCount / totalInboxTasks) : 0;

      // Calcola il colore basato sulla percentuale di completamento
      let color = '#ffffff'; // Bianco di default

      if (totalInboxTasks > 0) {
        if (completionPercentage === 1) {
          // 100% completato - verde pieno
          color = 'rgb(76, 175, 80)';
        } else if (completionPercentage > 0) {
          // Parzialmente completato - dal verde desaturato al verde
          const intensity = completionPercentage;
          const green = Math.floor(120 + (55 * intensity)); // Da verde desaturato a verde
          const red = Math.floor(200 - (124 * intensity));
          const blue = Math.floor(200 - (120 * intensity));
          color = `rgb(${red}, ${green}, ${blue})`;
        } else {
          // Task in inbox ma non completati - grigio
          color = 'rgb(150, 150, 150)';
        }
      }

      days.push({
        date: new Date(currentDate),
        dateStr,
        completedTasks,
        totalInboxTasks,
        completedCount,
        completionPercentage,
        color,
        isCurrentMonth: currentDate.getMonth() === currentMonth,
        isToday: dateStr === Tasks.dateToLocalString(today)
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return {
      days,
      monthName: firstDay.toLocaleDateString('it-IT', { month: 'long', year: 'numeric' }),
      currentMonth,
      currentYear
    };
  }

  function closeDialog() {
    setDialogOpen(false);
    setEditingTask(null);
    setDescription('');
    setSelectedTags([]);
    setDeadline('');
    setPriorityType('');
    setNotes('');
    setTagError('');
    setTagInputValue('');
    setDateError('');
  }

  function goToNextPage() {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  }

  function goToPreviousPage() {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  }

  function goToPage(pageNumber) {
    if (pageNumber >= 0 && pageNumber < totalPages) {
      setCurrentPage(pageNumber);
    }
  }

  async function deleteTask(taskId) {
    try {
      await window.api.deleteTask(taskId);
      setTasks(prev => prev.filter(t => t.id !== taskId));
      setContextMenu(null);
      console.log('Task deleted and synced to iCloud');
    } catch (error) {
      console.error('Failed to delete task:', error);
    }
  }

  async function completeTask(taskId) {
    try {
      console.log('Completing task with ID:', taskId);
      const result = await window.api.completeTask(taskId);
      console.log('Complete task result:', result);
      setTasks(prev => prev.map(t =>
        t.id === taskId
          ? { ...t, completed: result.completed, completedAt: result.completedAt }
          : t
      ));
      setContextMenu(null);
      console.log('Task completed and synced to iCloud');
    } catch (error) {
      console.error('Failed to complete task:', error);
    }
  }

  function handleTagChange(event, newValue) {
    setTagError('');

    // Validate each new tag
    for (const tag of newValue) {
      const validation = Tasks.validateTag(tag);
      if (!validation.valid) {
        setTagError(validation.error);
        return; // Don't update tags if validation fails
      }
    }

    // If all tags are valid, update the selected tags
    setSelectedTags(newValue);
  }

  function handleTagInputChange(event, newInputValue) {
    setTagInputValue(newInputValue);
    // Pulisci l'errore quando l'utente inizia a digitare
    if (tagError) {
      setTagError('');
    }
  }

  function handleDeadlineChange(event) {
    setDeadline(event.target.value);
    // Pulisci l'errore della data quando l'utente seleziona una data
    if (dateError) {
      setDateError('');
    }
  }

  function toggleFolder(folder) {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folder)) {
        newSet.delete(folder);
      } else {
        newSet.add(folder);
      }
      return newSet;
    });
  }

  function addPendingTagIfExists() {
    // Se c'è del testo digitato ma non confermato, aggiungilo ai tag
    if (tagInputValue.trim()) {
      const validation = Tasks.validateTag(tagInputValue.trim());
      if (validation.valid) {
        const newTag = validation.tag;
        // Aggiungi solo se non è già presente
        if (!selectedTags.includes(newTag)) {
          const updatedTags = [...selectedTags, newTag];
          setSelectedTags(updatedTags);
          setTagInputValue('');
          setTagError('');
          return updatedTags; // Ritorna i tag aggiornati
        } else {
          setTagInputValue('');
          return selectedTags; // Tag già presente, ritorna i tag esistenti
        }
      } else {
        setTagError(validation.error);
        return null; // Errore di validazione
      }
    }
    return selectedTags; // Nessun tag pendente, ritorna i tag esistenti
  }

  async function onSubmit(ev) {
    ev.preventDefault();
    if (!description.trim()) return;

    // Validazione data obbligatoria per Evento e Deadline
    if (needsDate && !deadline.trim()) {
      setDateError('La data è obbligatoria per le priorità "Evento" e "Deadline"');
      return;
    }

    // Prima di salvare, aggiungi eventuali tag non confermati
    const finalTags = addPendingTagIfExists();
    if (finalTags === null) {
      return; // Se c'è un errore di validazione, non continuare
    }

    try {
      if (isEditing) {
        // Update existing task
        const updatedTask = {
          id: editingTask.id,
          name: description.trim(),
          tags: finalTags,
          date: needsDate ? deadline : null,
          type: priorityType,
          notes: notes.trim()
        };

        await window.api.updateTask(updatedTask);

        // Update local state with the correct format
        const localTask = {
          ...editingTask,
          description: description.trim(),
          tags: finalTags,
          deadline: needsDate ? deadline : null,
          priority: priorityType,
          notes: notes.trim()
        };
        setTasks(prev => prev.map(t => t.id === editingTask.id ? localTask : t));
        console.log('Task updated and synced to iCloud');
      } else {
        // Create new task
        const newTask = {
          name: description.trim(),
          tags: finalTags,
          date: needsDate ? deadline : null,
          type: priorityType,
          notes: notes.trim()
        };

        const taskId = await window.api.addTask(newTask);

        // Add to local state with the correct format
        const localTask = Tasks.createTask(description.trim(), finalTags, needsDate ? deadline : null, priorityType, notes.trim());
        localTask.id = taskId;
        setTasks(prev => [...prev, localTask]);
        console.log('Task created and synced to iCloud');
      }

      // Reset form
      setDescription('');
      setSelectedTags([]);
      setDeadline('');
      setPriorityType('');
      setNotes('');
      setTagError('');
      setTagInputValue('');
      setDateError('');
      setEditingTask(null);
      setDialogOpen(false);
    } catch (error) {
      console.error('Failed to save task:', error);
      // TODO: Show error message to user
    }
  }

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={() => setDrawerOpen(!drawerOpen)}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Red Tasks
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Sidebar Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto', p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Filtri
          </Typography>

          <List>
            <ListItemButton
              selected={viewMode === 'inbox'}
              onClick={() => {
                setViewMode('inbox');
                setSearchQuery('');
              }}
            >
              <InboxIcon />
              <ListItemText primary="Inbox" sx={{ ml: 1 }} />
            </ListItemButton>

            <ListItemButton
              selected={viewMode === 'all'}
              onClick={() => {
                setViewMode('all');
                setSearchQuery('');
              }}
            >
              <ViewListIcon />
              <ListItemText primary="All" sx={{ ml: 1 }} />
            </ListItemButton>

            <ListItemButton
              selected={viewMode === 'calendario'}
              onClick={() => {
                setViewMode('calendario');
                setSearchQuery('');
              }}
            >
              <CalendarTodayIcon />
              <ListItemText primary="Calendario" sx={{ ml: 1 }} />
            </ListItemButton>

            <Divider sx={{ my: 1 }} />

            {/* Folders */}
            {folders.map(folder => {
              const isExpanded = expandedFolders.has(folder);
              const folderTags = Tasks.getTagsInFolder(tags, folder);

              return (
                <React.Fragment key={folder}>
                  <ListItemButton onClick={() => toggleFolder(folder)}>
                    {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                    {isExpanded ? <FolderOpenIcon /> : <FolderIcon />}
                    <ListItemText primary={folder} sx={{ ml: 1 }} />
                  </ListItemButton>

                  {isExpanded && folderTags.map(tag => (
                    <ListItemButton
                      key={tag}
                      selected={viewMode === tag}
                      onClick={() => {
                        setViewMode(tag);
                        setSearchQuery('');
                      }}
                      sx={{ pl: 6 }}
                    >
                      <LabelIcon />
                      <ListItemText primary={Tasks.getTagNameFromTag(tag)} sx={{ ml: 1 }} />
                    </ListItemButton>
                  ))}
                </React.Fragment>
              );
            })}

            {/* Standalone Tags */}
            {standaloneTags.length > 0 && (
              <>
                {folders.length > 0 && <Divider sx={{ my: 1 }} />}
                {standaloneTags.map(tag => (
                  <ListItemButton
                    key={tag}
                    selected={viewMode === tag}
                    onClick={() => {
                      setViewMode(tag);
                      setSearchQuery('');
                    }}
                  >
                    <LabelIcon />
                    <ListItemText primary={tag} sx={{ ml: 1 }} />
                  </ListItemButton>
                ))}
              </>
            )}
          </List>

          {/* iCloud Status */}
          <Box sx={{ position: 'absolute', bottom: 16, left: 16, right: 16 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              p: 1,
              backgroundColor: 'background.paper',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'divider'
            }}>
              {syncStatus?.iCloudAvailable ? <CloudIcon /> : <CloudOffIcon />}
              <Box sx={{ flex: 1 }}>
                <Typography variant="caption" display="block">
                  {syncStatus?.iCloudAvailable ? 'iCloud Sync' : 'iCloud Non Disponibile'}
                </Typography>
                {syncStatus?.lastSync && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    {new Date(syncStatus.lastSync).toLocaleString('it-IT')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />

        <Container maxWidth="md">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4">
              {viewMode === 'inbox' ? 'Inbox' :
               viewMode === 'all' ? 'All Tasks' :
               viewMode === 'calendario' ? 'Calendario' :
               `Tasks: ${viewMode}`}
            </Typography>
            {viewMode !== 'calendario' && (
              <Chip
                label={`${displayed.length} task${displayed.length !== 1 ? 's' : ''}`}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}
          </Box>

          {/* Search Box for All mode */}
          {viewMode === 'all' && (
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Cerca task..."
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'background.paper',
                  },
                }}
              />
            </Box>
          )}



          {/* Tasks List or Calendar */}
          <Box sx={{ position: 'relative', minHeight: 'calc(100vh - 300px)' }}>
            {viewMode === 'calendario' ? (
              /* Calendar View */
              <Box sx={{ py: 2 }}>
                <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
                  {calendarData?.monthName}
                </Typography>

                {/* Calendar Grid */}
                <Box sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(7, 1fr)',
                  gap: 1,
                  maxWidth: '800px',
                  margin: '0 auto'
                }}>
                  {/* Header giorni della settimana */}
                  {['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'].map(day => (
                    <Box key={day} sx={{
                      p: 1,
                      textAlign: 'center',
                      fontWeight: 'bold',
                      color: 'text.secondary'
                    }}>
                      {day}
                    </Box>
                  ))}

                  {/* Giorni del calendario */}
                  {calendarData?.days.map((day, index) => (
                    <Box
                      key={index}
                      sx={{
                        aspectRatio: '1',
                        backgroundColor: day.color,
                        border: '1px solid',
                        borderColor: day.isToday ? 'primary.main' : 'divider',
                        borderWidth: day.isToday ? 2 : 1,
                        borderRadius: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: day.totalInboxTasks > 0 ? 'pointer' : 'default',
                        opacity: day.isCurrentMonth ? 1 : 0.3,
                        transition: 'all 0.2s ease-in-out',
                        '&:hover': day.totalInboxTasks > 0 ? {
                          transform: 'scale(1.05)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                        } : {}
                      }}
                      title={day.totalInboxTasks > 0 ?
                        `${day.completedCount}/${day.totalInboxTasks} task completati (${Math.round(day.completionPercentage * 100)}%)` :
                        'Nessun task in inbox'
                      }
                    >
                      <Typography variant="body2" sx={{
                        fontWeight: day.isToday ? 'bold' : 'normal',
                        color: day.totalInboxTasks > 0 && day.completionPercentage > 0 ? 'white' : 'text.primary'
                      }}>
                        {day.date.getDate()}
                      </Typography>
                      {day.totalInboxTasks > 0 && day.completionPercentage > 0 && (
                        <Typography variant="caption" sx={{
                          color: 'white',
                          fontSize: '0.7rem',
                          fontWeight: 'bold'
                        }}>
                          {Math.round(day.completionPercentage * 100)}%
                        </Typography>
                      )}
                    </Box>
                  ))}
                </Box>

                {/* Legenda */}
                <Box sx={{ mt: 4, textAlign: 'center' }}>
                  <Typography variant="h6" gutterBottom>
                    Legenda
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, flexWrap: 'wrap' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 20, height: 20, backgroundColor: '#ffffff', border: '1px solid #ccc', borderRadius: 1 }} />
                      <Typography variant="body2">Nessun task in inbox</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 20, height: 20, backgroundColor: 'rgb(150, 150, 150)', borderRadius: 1 }} />
                      <Typography variant="body2">Task in inbox ma non completati</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 20, height: 20, backgroundColor: 'rgb(200, 139, 200)', borderRadius: 1 }} />
                      <Typography variant="body2">Parzialmente completati (1-99%)</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 20, height: 20, backgroundColor: 'rgb(76, 175, 80)', borderRadius: 1 }} />
                      <Typography variant="body2">Tutti i task completati (100%)</Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            ) : (
              /* Tasks List */
              <Stack
                spacing={3}
                sx={{
                  py: 2,
                }}
              >
              {displayed.length === 0 ? (
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  {viewMode === 'inbox' ? 'Nessuna task presente' :
                   viewMode === 'all' ? (searchQuery ? `Nessuna task trovata per "${searchQuery}"` : 'Nessuna task presente') :
                   `Nessuna task con tag "${viewMode}"`}
                </Typography>
              ) : (
              displayed.map((task, index) => (
                <Card
                  key={task.id}
                  variant="outlined"
                  onClick={() => handleTaskClick(task)}
                  onContextMenu={(e) => handleContextMenu(e, task)}
                  sx={{
                    cursor: 'pointer',
                    transition: 'box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out',
                    '&:hover': {
                      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                      transform: 'translateY(-2px)',
                    },
                    // Stile condizionale per evidenziare task con deadline o ASAP
                    ...( (task.priority === 'Deadline' || task.priority === 'ASAP') && {
                      borderLeft: `4px solid ${theme.palette.error.main}`,
                    })
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                        {task.description}
                      </Typography>
                      {task.priority && (
                        <Chip
                          label={task.priority}
                          size="small"
                          color={task.priority === 'ASAP' ? 'error' : task.priority === 'Deadline' ? 'warning' : 'primary'}
                          variant="filled"
                        />
                      )}
                    </Box>

                    {task.deadline && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Scadenza: {new Date(task.deadline).toLocaleDateString('it-IT')}
                      </Typography>
                    )}

                    {task.tags.length > 0 && (
                      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                        {task.tags.map(tag => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            onClick={() => {
                              setViewMode(tag);
                              setSearchQuery('');
                            }}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Stack>
                    )}

                    {/* Note preview */}
                    {task.notes && task.notes.trim() && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
                        {task.notes.length > 100 ? `${task.notes.substring(0, 100)}...` : task.notes}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
            </Stack>
            )}

            {/* Pagination Controls */}
            {viewMode !== 'calendario' && totalPages > 1 && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  mt: 4,
                  gap: 2,
                }}
              >
                <IconButton
                  onClick={goToPreviousPage}
                  disabled={currentPage === 0}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 1)',
                    },
                    '&:disabled': {
                      backgroundColor: 'rgba(255, 255, 255, 0.5)',
                    },
                  }}
                >
                  <KeyboardArrowUpIcon />
                </IconButton>

                {/* Page indicators */}
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                  {Array.from({ length: totalPages }, (_, index) => (
                    <Box
                      key={index}
                      onClick={() => goToPage(index)}
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: index === currentPage ? theme.palette.primary.main : 'rgba(0,0,0,0.2)',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease-in-out',
                        '&:hover': {
                          backgroundColor: index === currentPage ? theme.palette.primary.dark : 'rgba(0,0,0,0.4)',
                          transform: 'scale(1.2)',
                        },
                      }}
                    />
                  ))}
                </Box>

                <IconButton
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages - 1}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 1)',
                    },
                    '&:disabled': {
                      backgroundColor: 'rgba(255, 255, 255, 0.5)',
                    },
                  }}
                >
                  <KeyboardArrowDownIcon />
                </IconButton>
              </Box>
            )}

            {/* Page Info */}
            {viewMode !== 'calendario' && allDisplayed.length > 0 && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  mt: 2,
                  gap: 1,
                }}
              >
                <Typography variant="caption" color="text.secondary">
                  Pagina {currentPage + 1} di {totalPages} • {allDisplayed.length} task totali
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                  {tasksPerPage} task per pagina (adattato alla finestra {windowHeight}px)
                </Typography>
                {totalPages > 1 && (
                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                    ← → per pagine • ↑ ↓ per task • 1-{Math.min(displayed.length, 9)} per saltare
                  </Typography>
                )}
              </Box>
            )}
          </Box>
        </Container>
      </Box>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={openCreateDialog}
      >
        <AddIcon />
      </Fab>

      {/* Add/Edit Task Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={closeDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{isEditing ? 'Modifica task' : 'Aggiungi nuova task'}</DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={onSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              label="Descrizione"
              variant="outlined"
              fullWidth
              value={description}
              onChange={(ev) => setDescription(ev.target.value)}
              required
              autoFocus
            />

            <Autocomplete
              multiple
              options={tags}
              value={selectedTags}
              onChange={handleTagChange}
              inputValue={tagInputValue}
              onInputChange={handleTagInputChange}
              freeSolo
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Seleziona o aggiungi tags (es: Folder/Tag)"
                  helperText={tagError || "Formato: 'Tag' o 'Folder/Tag' (massimo un livello di nesting)"}
                  error={!!tagError}
                />
              )}
            />

            <FormControl>
              <FormLabel component="legend">Priorità</FormLabel>
              <Stack direction="row" spacing={1} sx={{ mt: 1, flexWrap: 'wrap', gap: 1 }}>
                {priorityTypes.map((type) => (
                  <Chip
                    key={type}
                    label={type}
                    clickable
                    color={priorityType === type ? 'primary' : 'default'}
                    variant={priorityType === type ? 'filled' : 'outlined'}
                    onClick={() => setPriorityType(priorityType === type ? '' : type)}
                  />
                ))}
              </Stack>
            </FormControl>

            {needsDate && (
              <TextField
                label="Data"
                type="date"
                variant="outlined"
                value={deadline}
                onChange={handleDeadlineChange}
                InputLabelProps={{
                  shrink: true,
                }}
                required
                error={!!dateError}
                helperText={dateError || `Data obbligatoria per ${priorityType}`}
              />
            )}

            <TextField
              label="Note"
              variant="outlined"
              fullWidth
              multiline
              rows={3}
              value={notes}
              onChange={(ev) => setNotes(ev.target.value)}
              placeholder="Aggiungi note opzionali..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Annulla</Button>
          <Button
            onClick={onSubmit}
            variant="contained"
            disabled={!description.trim() || !!tagError || !!dateError || (needsDate && !deadline.trim())}
          >
            {isEditing ? 'Salva Modifiche' : 'Aggiungi Task'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Task Details Dialog */}
      <Dialog
        open={taskDetailsOpen}
        onClose={closeTaskDetails}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            Dettagli Task
            <IconButton onClick={closeTaskDetails} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedTaskForDetails && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Descrizione
                </Typography>
                <Typography variant="body1">
                  {selectedTaskForDetails.description}
                </Typography>
              </Box>

              {selectedTaskForDetails.priority && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Priorità
                  </Typography>
                  <Chip
                    label={selectedTaskForDetails.priority}
                    color={selectedTaskForDetails.priority === 'ASAP' ? 'error' :
                           selectedTaskForDetails.priority === 'Deadline' ? 'warning' : 'primary'}
                    variant="filled"
                  />
                </Box>
              )}

              {selectedTaskForDetails.deadline && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Data
                  </Typography>
                  <Typography variant="body1">
                    {new Date(selectedTaskForDetails.deadline).toLocaleDateString('it-IT')}
                  </Typography>
                </Box>
              )}

              {selectedTaskForDetails.tags && selectedTaskForDetails.tags.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Tags
                  </Typography>
                  <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                    {selectedTaskForDetails.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Stack>
                </Box>
              )}

              {selectedTaskForDetails.notes && selectedTaskForDetails.notes.trim() && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Note
                  </Typography>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {selectedTaskForDetails.notes}
                  </Typography>
                </Box>
              )}


            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => openEditDialog(selectedTaskForDetails)} variant="outlined">
            Modifica
          </Button>
          <Button onClick={closeTaskDetails}>
            Chiudi
          </Button>
        </DialogActions>
      </Dialog>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextMenu?.task && !contextMenu.task.completed && (
          <MenuItem onClick={() => {
            completeTask(contextMenu.task.id);
            closeContextMenu();
          }}>
            <CheckCircleIcon sx={{ mr: 1 }} />
            Completa
          </MenuItem>
        )}
        <MenuItem onClick={() => openEditDialog(contextMenu?.task)}>
          <EditIcon sx={{ mr: 1 }} />
          Modifica
        </MenuItem>
        <MenuItem onClick={() => deleteTask(contextMenu?.task?.id)}>
          <DeleteIcon sx={{ mr: 1 }} />
          Elimina
        </MenuItem>
      </Menu>
    </Box>
  );
}

// Render dell'app
const domContainer = document.getElementById('root');
const root = ReactDOM.createRoot(domContainer);
root.render(
  <ThemeProvider theme={theme}>
    <App />
  </ThemeProvider>
);
