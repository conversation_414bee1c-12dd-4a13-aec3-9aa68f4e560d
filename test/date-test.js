// Test per verificare la gestione corretta delle date
const Tasks = require('../src/tasks.js');

console.log('Testing date handling...');

// Simula diversi fusi orari e orari
const now = new Date();
console.log('Current time:', now.toString());
console.log('Current time UTC:', now.toISOString());

// Test della funzione dateToLocalString
const localDateStr = Tasks.dateToLocalString(now);
console.log('Local date string:', localDateStr);

// Test con UTC (vecchio metodo)
const utcDateStr = now.toISOString().split('T')[0];
console.log('UTC date string:', utcDateStr);

// Verifica che siano uguali quando non ci sono problemi di fuso orario
console.log('Are they equal?', localDateStr === utcDateStr);

// Test con orari critici (23:00 locale quando UTC è già il giorno dopo)
const lateEvening = new Date();
lateEvening.setHours(23, 30, 0, 0); // 23:30 locale

console.log('\nTesting late evening (23:30):');
console.log('Late evening local:', lateEvening.toString());
console.log('Late evening UTC:', lateEvening.toISOString());
console.log('Local date string:', Tasks.dateToLocalString(lateEvening));
console.log('UTC date string:', lateEvening.toISOString().split('T')[0]);

// Test con task di oggi
const todayTask = Tasks.createTask('Test task', [], localDateStr, 'Evento');
console.log('\nCreated task for today:', todayTask);

// Test inbox con task di oggi
const testTasks = [todayTask];
const inboxTasks = Tasks.getInboxTasks(testTasks);
console.log('Inbox tasks:', inboxTasks.length);
console.log('Task should be in inbox:', inboxTasks.length > 0);

console.log('\nDate handling test completed!');
