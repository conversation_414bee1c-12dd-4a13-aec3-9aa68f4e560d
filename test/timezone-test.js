// Test per verificare il problema del fuso orario
const Tasks = require('../src/tasks.js');

console.log('Testing timezone edge case...');

// Simula il caso problematico: 30 maggio alle 23:00 in Italia (UTC+2)
// che in UTC diventa 31 maggio alle 21:00
const problematicDate = new Date('2025-05-30T23:00:00+02:00');

console.log('Problematic date (local):', problematicDate.toString());
console.log('Problematic date (UTC):', problematicDate.toISOString());

// Vecchio metodo (problematico)
const oldMethod = problematicDate.toISOString().split('T')[0];
console.log('Old method (UTC-based):', oldMethod);

// Nuovo metodo (corretto)
const newMethod = Tasks.dateToLocalString(problematicDate);
console.log('New method (local-based):', newMethod);

console.log('Difference found:', oldMethod !== newMethod);

// Test con task creato il 30 maggio
const task30 = Tasks.createTask('Task del 30', [], '2025-05-30', 'Evento');

// Simula che oggi sia il 30 maggio alle 23:00
// Impostiamo manualmente la data per il test
const originalDateToLocalString = Tasks.dateToLocalString;
Tasks.dateToLocalString = function(date) {
  if (arguments.length === 0 || date === undefined) {
    // Se chiamato senza argomenti (come in getInboxTasks), restituisce il 30
    return '2025-05-30';
  }
  return originalDateToLocalString(date);
};

// Ora testiamo l'inbox
const testTasks = [task30];
const inboxTasks = Tasks.getInboxTasks(testTasks);

console.log('\nTesting inbox with task for May 30th:');
console.log('Task deadline:', task30.deadline);
console.log('Simulated today:', '2025-05-30');
console.log('Task in inbox:', inboxTasks.length > 0);
console.log('Expected: true (task should be in inbox)');

// Ripristina la funzione originale
Tasks.dateToLocalString = originalDateToLocalString;

console.log('\nTimezone test completed!');
